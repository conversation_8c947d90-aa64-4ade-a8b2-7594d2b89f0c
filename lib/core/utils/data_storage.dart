import 'package:connectone/bai_models/request_types_res.dart';

import '../../old_models/configuration.dart';

class DataStorage {
  static List<Configuration?>? configData;
  static String? oldStockId;
  static List<RequestTypes>? requestTypes;
  static bool shouldShowOptionsMenu = false;
  static int? targetItemIdForOptionsMenu;

  // Method to clear the old stock ID
  static void clearOldStockId() {
    oldStockId = null;
  }

  // Method to check if configuration data is available
  static bool hasConfigData() {
    return configData != null && configData!.isNotEmpty;
  }
}
